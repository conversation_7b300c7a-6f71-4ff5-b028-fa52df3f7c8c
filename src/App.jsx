import React from "react";
import Layout from "./components/layout/Layout";
import "./utilty/i18n";
import { Routes, Route, Outlet } from "react-router-dom";
import LayoutAr from "./components/layout/ar/LayoutAr";

const App = () => {
  return (
    <>
      {" "}
      <Routes>
        <Route path="/en/*" element={<Layout />}></Route>
        <Route path="/ar/*" element={<LayoutAr />}></Route>
      </Routes>
      <Outlet />
    </>
  );
};

export default App;
