import React from 'react'
import HeaderAr from './HeaderAr'
import FooterAr from './FooterAr'
import { Routes, Route } from "react-router-dom";
import HomeAr from '../../../pages/HomeAr';
import ArticalAr from '../../../pages/ArticalAr';
const LayoutAr = () => {
  return (
    <div>
      <HeaderAr/>
      <Routes>
        <Route index element={<HomeAr />} />
        <Route path="home" element={<HomeAr />} />
        <Route path="artical" element={<ArticalAr />} />
      </Routes>
      <FooterAr/>
    </div>
  )
}

export default LayoutAr