import React, { useEffect, useState } from "react";
import logo from "../../../assets/images/logo/logo.svg";
import humperger from "../../../assets/images/icons/humperger.svg";
import search from "../../../assets/images/icons/search.svg";
import language from "../../../assets/images/icons/language.svg";
import ModalUl from "../../parts/ModalUl";
import { useNavigate, Link } from "react-router-dom";

const HeaderAr = () => {
  const navigate = useNavigate();
  const [showHeader, setShowHeader] = useState(true);

  useEffect(() => {
    // Check the current route and update the dir attribute accordingly
    const isArabicRoute = location.pathname.startsWith("/ar");
    const rootHtml = document.getElementById("root-html");

    if (isArabicRoute) {
      rootHtml.setAttribute("dir", "rtl");
    } else {
      rootHtml.setAttribute("dir", "ltr");
    }
  }, [location.pathname]);

  const dataService = [
    {
      id: 1,
      title: "العربية",
      url: "/",
    },
    {
      id: 2,
      title: "التركية",
      url: "/",
    },
    {
      id: 3,
      title: "الأوروبية",
      url: "/",
    },
    {
      id: 4,
      title: "الدولية",
      url: "/",
    },
    {
      id: 5,
      title: "الدراسة الجامعية",
      url: "/",
    },
  ];
  return (
    <div className="px-12">
      <div className="flex  items-center max-lg:space-x-4 justify-between">
        <div className="flex  lg:space-x-10 md:space-x-6 sm:space-x-4 max-sm:space-x-2 mt-4 h-auto items-center rtl:space-x-reverse ">
          <div
            className="cursor-pointer lg:hidden "
            onClick={() => {
              setShowHeader(!showHeader);
            }}
          >
            <img src={humperger} alt="humperger" />
          </div>
          {showHeader && (
            <div className="flex lg:space-x-10  md:space-x-6 sm:space-x-4 max-sm:space-x-2  h-auto items-center rtl:space-x-reverse">
              <div>
                <div className="px-3  py-2 rounded-xl  bg-gray-200 flex">
                  <img src={search} alt="" />
                  <input
                    className="w-full ps-4  font-bold bg-gray-200 text-gray-800 xl:text-xl  placeholder:max-lg:text-sm lg:text-lg md:text-md sm:text-sm max-sm:text-sm placeholder:text-gray-500 placeholder:text-lg  placeholder:font-bold   focus:outline-none"
                    placeholder="البحث عن مقالة "
                    type="text"
                  />
                </div>
              </div>
              <div>
                <h3 className="font-bold text-gray-800  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm">
                  <button onClick={() => navigate("/en")}>
                    <svg
                      width={25}
                      height={30}
                      className="rounded-lg"
                      xmlns="http://www.w3.org/2000/svg"
                      xmlns:xlink="http://www.w3.org/1999/xlink"
                      viewBox="0 0 7410 3900"
                    >
                      <path fill="#b22234" d="M0 0h7410v3900H0z" />
                      <path
                        d="M0 450h7410m0 600H0m0 600h7410m0 600H0m0 600h7410m0 600H0"
                        stroke="#fff"
                        stroke-width="300"
                      />
                      <path fill="#3c3b6e" d="M0 0h2964v2100H0z" />
                      <g fill="#fff">
                        <g id="d">
                          <g id="c">
                            <g id="e">
                              <g id="b">
                                <path
                                  id="a"
                                  d="M247 90l70.534 217.082-184.66-134.164h228.253L176.466 307.082z"
                                />
                                <use xlink:href="#a" y="420" />
                                <use xlink:href="#a" y="840" />
                                <use xlink:href="#a" y="1260" />
                              </g>
                              <use xlink:href="#a" y="1680" />
                            </g>
                            <use xlink:href="#b" x="247" y="210" />
                          </g>
                          <use xlink:href="#c" x="494" />
                        </g>
                        <use xlink:href="#d" x="988" />
                        <use xlink:href="#c" x="1976" />
                        <use xlink:href="#e" x="2470" />
                      </g>
                    </svg>
                  </button>
                </h3>
              </div>
              <div>
                <h3 className="font-semibold text-green-800  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm">
                  <Link to="artical"> المقالات</Link>
                </h3>
              </div>
            </div>
          )}
        </div>
        <div>
          <div className="bg-green-600  xl:w-28 lg:w-24 md:w-20 sm:w-16  max-sm:w-14 rounded-b-xl">
            <Link to="home">
              {" "}
              <img src={logo} alt="logo" />
            </Link>
          </div>
        </div>
      </div>{" "}
      {showHeader && (
        <div className=" max-lg:mt-4 py-2">
          <ul className="flex lg:space-x-4 mb-2  flex-wrap  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm font-semibold text-gray-600  rtl:space-x-reverse ">
            <ModalUl
              className="flex lg:flex-row flex-col lg:rtl:space-x-reverse  "
              title="خدماتنا"
              subtitle={dataService}
            />
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>التعليم</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>اللغات</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>المنح</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>الاختبارات</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>{" "}
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>التخصصات الجامعية</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <ModalUl
              className=" flex lg:flex-row flex-col lg:rtl:space-x-reverse"
              title="الجامعات"
              subtitle={dataService}
            />
          </ul>
        </div>
      )}
    </div>
  );
};

export default HeaderAr;
