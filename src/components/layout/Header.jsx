import React, { useState, useEffect } from "react";
import logo from "../../assets/images/logo/logo.svg";
import humperger from "../../assets/images/icons/humperger.svg";
import search from "../../assets/images/icons/search.svg";
import ModalUl from "../parts/ModalUl";

import { useNavigate, Link } from "react-router-dom";

const Header = () => {
  const navigate = useNavigate();
  const [showHeader, setShowHeader] = useState(true);
  
  useEffect(() => {
    // Check the current route and update the dir attribute accordingly
    const isArabicRoute = location.pathname.startsWith("/ar");
    const rootHtml = document.getElementById("root-html");

    if (isArabicRoute) {
      rootHtml.setAttribute("dir", "rtl");
    } else {
      rootHtml.setAttribute("dir", "ltr");
    }
  }, [location.pathname]);

  const dataService = [
    {
      id: 1,
      title: "arabic",
      url: "/",
    },
    {
      id: 2,
      title: "Turkish",
      url: "/",
    },
    {
      id: 3,
      title: "European",
      url: "/",
    },
    {
      id: 4,
      title: "International",
      url: "/",
    },
    {
      id: 5,
      title: "University Education",
      url: "/",
    },
  ];
  return (
    <div className="px-12">
      <div className="flex  max-lg:space-x-4 justify-between">
        <div className="flex  lg:space-x-10 md:space-x-6 sm:space-x-4 max-sm:space-x-2 mt-4 h-auto items-center rtl:space-x-reverse ">
          <div
            className="cursor-pointer lg:hidden "
            onClick={() => {
              setShowHeader(!showHeader);
            }}
          >
            <img src={humperger} alt="humperger" />
          </div>
          {showHeader && (
            <div className="flex lg:space-x-10 md:space-x-6 sm:space-x-4 max-sm:space-x-2  h-auto items-center rtl:space-x-reverse">
              <div>
                <div className="px-3  py-2 rounded-xl  bg-gray-200 flex">
                  <img src={search} alt="" />
                  <input
                    className="w-full ps-4  font-bold bg-gray-200 text-gray-800 xl:text-xl  placeholder:max-lg:text-sm lg:text-lg md:text-md sm:text-sm max-sm:text-sm placeholder:text-gray-500 placeholder:text-lg  placeholder:font-bold   focus:outline-none"
                    placeholder="Searching for Articles "
                    type="text"
                  />
                </div>
              </div>
              <div>
                <h3 className="font-bold text-gray-800  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm">
                  <button onClick={() => navigate("/ar")}>
                    <svg
                      width={30}
                      height={30}
                      className="rounded-lg"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 900 600"
                    >
                      <path d="M900 600V0H0v600z" fill="#006c35" />
                      <path
                        d="M636.911 160.576c5.1-3.635 9.844-7.712 15.52-10.576-1.622 6.5-6.486 11.457-11.466 15.644-2.548 1.983-1.39 5.618-1.738 8.372a56.044 56.044 0 0 1-4.053-1.1c1.274 16.525 3.127 32.94 3.938 49.465.115 1.873.231 3.856 1.853 5.068 7.643 6.83 15.519 13.55 21.078 22.253 2.2 3.416 3.359 7.271 4.517 11.127a61.162 61.162 0 0 0 .579-19.5q-4.168-37.677-7.528-75.575c-.231-3.085-.695-6.28-.347-9.364a5.831 5.831 0 0 1 6.022-5.068 14.487 14.487 0 0 1 3.591 5.4c2.316 5.178 4.4 10.8 3.821 16.635-1.389-.44-2.779-.991-4.285-1.542 2.085 17.076 4.17 34.152 5.444 51.338.579 11.568 2.316 23.245-.116 34.7-2.085 10.576-7.3 21.262-16.562 27.321-7.76 5.068-16.793 7.932-25.827 9.805a23.722 23.722 0 0 1-11.119.11c3.822-2.093 8.108-3.635 12.161-5.4a105.889 105.889 0 0 0 16.909-8.813c4.981-3.305 10.192-6.61 13.9-11.458-4.633-12.338-12.277-23.906-23.164-31.508.348 12.119.927 24.458-.926 36.466a12.53 12.53 0 0 1-3.591 7.381c-1.274-5.068-1.389-10.356-1.969-15.534a1667.379 1667.379 0 0 1-2.779-33.821c0-2.313-1.506-4.076-3.243-5.4-6.833-5.729-13.2-12.119-19.8-18.068.232 6.941 1.274 13.771 1.738 20.6.81 9.254.926 18.508 1.853 27.762.579 6.5.694 13.33-1.738 19.5-2.432 5.839-7.875 10.8-14.361 11.238a40.258 40.258 0 0 1-10.308-.551c-3.59-.551-6.37-3.3-7.991-6.39-1.969-3.746-3.011-7.932-4.633-11.788-3.011.661-6.138 1.322-8.339 3.636-4.632 4.406-6.022 11.127-8.223 16.855-1.274 3.415-2.779 7.161-6.138 9.034-3.474 1.983-7.76 1.212-11.118-.661-5.1-2.754-8.223-8.042-9.729-13.44-1.506-4.187.579-9.034-1.969-13-1.853-3.636-.811-7.932.116-11.678 1.969-6.72 6.138-12.449 11.118-17.3l-19.8-17.186c.579 15.864 1.853 31.728-.232 47.482a13.782 13.782 0 0 1-3.822 8.483 60.231 60.231 0 0 1-1.39-9.144q-2.431-26.11-4.169-52.219c-.116-1.984-.463-4.187-2.085-5.509a253.1 253.1 0 0 1-19.225-20.381c-3.475-4.406-7.065-8.923-8.918-14.211-.695-2.2-1.274-5.288.81-6.941a18.117 18.117 0 0 1 6.718 2.424 46.865 46.865 0 0 1 10.539 8.372 12.432 12.432 0 0 1 2.664 5.178c-1.969 0-3.822-.11-5.791-.11a77.027 77.027 0 0 0 13.9 15.2c-.463-14.762-2.085-29.635-2.78-44.4 0-1.983 0-4.186 1.39-5.839 1.274-1.432 3.243-1.983 4.98-2.644a47.975 47.975 0 0 1 6.949 15.644 22.379 22.379 0 0 1 .348 6.83c-1.39-.33-2.78-.771-4.054-1.212.927 12.008 2.2 23.907 3.012 35.915a25.351 25.351 0 0 0 .694 5.067 13.023 13.023 0 0 0 2.664 2.865c8.223 6.94 16.446 13.881 24.785 20.6 4.98-4.3 9.844-8.7 14.825-12.889a27.164 27.164 0 0 0 3.242-2.975c.927-.991.58-2.424.58-3.635-1.622-14.432-2.78-28.864-4.054-43.3-.348-5.288-1.158-10.466-1.158-15.644-.116-3.085 2.316-6.169 5.443-6.279 1.39-.111 1.969 1.432 2.664 2.313a47.8 47.8 0 0 1 5.1 13.11 15.148 15.148 0 0 1-.116 6.83c-1.274-.551-2.664-1.212-3.938-1.762 1.738 13.55 3.127 27.1 4.4 40.651 3.591-2.644 6.834-5.728 10.192-8.593 3.706-3.195 7.644-6.279 11.234-9.694-4.285-4.627-8.8-8.924-13.2-13.441-2.779-2.864-6.138-5.618-7.3-9.7-.927-2.864-.348-5.949.232-8.7 5.211.882 9.728 3.966 13.9 7.051a81.426 81.426 0 0 1 7.991 7.271c-.232-5.729-.927-11.457-1.39-17.186 0-2.2-.232-4.627 1.274-6.39 1.274-1.432 3.243-1.983 4.864-2.754a42.312 42.312 0 0 1 6.834 14.983 22.085 22.085 0 0 1 .579 7.271c-1.39-.331-2.9-.661-4.286-.992.464 4.407.927 8.7 1.506 13.11 5.443-4.076 10.424-8.7 15.867-12.779 1.853-1.763 5.212-3.085 5.212-6.17.116-3.305-.579-6.72-.116-10.025a5.818 5.818 0 0 1 6.022-4.957c2.78 2.644 3.822 5.949 5.328 8.813zm-28.259 31.838c-.927.992-2.664 1.873-2.548 3.526.695 1.432 2.084 2.534 3.243 3.745 6.717 6.06 13.087 12.449 20.152 17.958-.232-6.5-.927-12.89-1.274-19.39-.579-6.83-.811-13.661-1.506-20.491-6.254 4.517-11.929 9.805-18.067 14.652zm-17.025-12.008c1.5 1.653 2.9 3.305 4.4 4.958 0-1.653-.116-3.305-.116-4.958zm-16.215 40.211c-1.158.992-2.548 2.093-2.432 3.746-.116 8.373.811 16.966-1.274 25.118-.463 2.093-1.969 3.525-3.474 4.847-1.854-8.042-1.969-16.3-3.359-24.457-4.98 3.966-9.729 8.263-14.361 12.559 2.663 2.975 5.327 6.17 6.254 10.246a15.505 15.505 0 0 1-4.866 15.864c-5.675 4.737-13.319 5.508-20.383 6.39 1.042 4.627 1.968 9.474 4.748 13.44 1.737 2.314 4.517 4.517 7.528 3.636 3.243-.882 5.212-3.966 6.37-6.831a92.105 92.105 0 0 1 8.337-16.855c2.78-4.407 7.3-8.043 12.624-8.153 1.737.11 4.054-.44 5.1 1.432 2.316 3.856 3.127 8.483 6.022 12.119 4.865 6.389 15.172 6.5 20.963 1.212 1.969-1.763 1.969-4.627 1.738-7.051-2.664-21.923-4.633-44.067-6.95-65.99-7.76 6.059-15.057 12.449-22.585 18.728zm-40.883 37.457a16.79 16.79 0 0 0-2.548 5.839 24.763 24.763 0 0 0 15.056-3.195c1.39-.881 3.011-2.313 2.664-4.186-.348-3.746-3.475-6.39-6.254-8.593a85.5 85.5 0 0 0-8.918 10.135zM317.025 151.212c1.389-.882 2.663.661 3.59 1.652 2.78 3.416 5.791 6.831 7.412 11.017-1.39.22-2.895.441-4.285.661.347 6.61 5.907 11.127 7.528 17.406a24.392 24.392 0 0 1 .463 11.9 14.037 14.037 0 0 1-11 10.8c-6.37 1.1-13.551-.441-18.3-5.178-3.127-3.085-4.286-7.491-5.675-11.457-1.506 5.4-3.707 10.576-7.529 14.762a27.175 27.175 0 0 1-7.991 5.839c.811 14.762 2.316 29.525 1.042 44.177-.463 4.186-.694 9.144-4.516 11.9-1.39-5.839-1.622-11.9-2.317-17.957-1.274-12.229-2.2-24.457-3.474-36.686-3.591-.881-7.528-1.652-10.308-4.3-4.633-4.187-6.138-10.687-5.791-16.636.116-4.627 1.622-9.144 2.664-13.66a16.772 16.772 0 0 0-3.938 6.169c-2.432 7.381-4.053 15.093-7.412 22.144a10.061 10.061 0 0 1-6.717 5.728c-3.707.771-8.108.551-11-2.313-3.127-3.085-3.822-7.712-4.285-11.9-.232-4.3-.579-8.924 1.39-12.89 1.158-2.424 3.59-3.746 5.675-4.957-1.39 2.313-3.012 4.627-3.359 7.271-.463 3.745.232 7.6.926 11.237.464 2.2.927 4.627 2.664 6.169a7.42 7.42 0 0 0 9.729-.22 16.114 16.114 0 0 0 2.779-5.4c2.317-7.051 4.17-14.322 7.065-21.153 2.548-5.728 7.065-11.347 13.551-12.559-.695 7.822-4.285 15.093-4.517 23.025-.116 3.526.232 7.382 2.548 10.136 1.737 2.313 4.633 3.084 7.3 3.856-.694-10.687-1.737-21.373-2.432-32.169-.231-3.966-.926-8.042-.231-12.008.579-2.755 3.59-3.746 5.906-4.627 4.4 6.5 7.644 14.211 7.528 22.143-1.5-.33-2.895-.661-4.4-.991.927 9.144 1.853 18.4 2.9 27.541a17.37 17.37 0 0 0 9.5-6.94c4.517-6.5 5.212-14.983 3.011-22.474-.927-3.195-2.432-6.17-3.127-9.475-.7-3.084 1.506-6.279 4.285-7.271 1.274-.55 1.853.992 2.432 1.873 1.738 3.746 2.317 7.932 3.127 12.008 1.043 5.4 0 11.017 1.969 16.195a20.027 20.027 0 0 0 8.687 10.8 16.839 16.839 0 0 0 11.118 1.653 4.991 4.991 0 0 0 3.822-3.746c.926-3.525-.347-7.161-1.853-10.355-3.475-7.272-7.76-14.212-9.381-22.144-.579-2.864.231-6.83 3.243-8.593z"
                        fill="#fff"
                      />
                      <path
                        d="M476.041 159.7a21.934 21.934 0 0 1 9.613-8.483 43.424 43.424 0 0 1-4.169 7.491c-1.159 1.763-1.159 3.966-1.274 5.949a37.754 37.754 0 0 0 7.875-1.212c-.695 2.094-1.39 4.3-3.359 5.619-2.2 1.542-5.443 2.534-7.759.661a8.634 8.634 0 0 1-.927-10.025zm-143.613-4.853c1.158-1.762 3.359-2.423 5.1-3.2a42.347 42.347 0 0 1 7.181 15.865 18.14 18.14 0 0 1 .116 6.61c-1.274-.441-2.664-.882-3.938-1.322 1.5 10.686 3.359 21.262 4.633 31.948 6.138-2.974 11.813-6.61 18.183-9.254a19.963 19.963 0 0 1 6.254-1.542c-.811 1.762-1.737 3.525-3.59 4.3-6.718 3.415-13.551 6.39-20.153 10.136.58 5.288 1.159 10.686 1.854 15.974 3.011-3.856 5.675-8.7 10.886-9.915.232 2.864-.695 6.279 1.39 8.593 3.243 2.534 7.644.991 10.655-1.322-.231-4.186.927-8.263 1.738-12.339 1.274.551 2.895.771 3.358 2.314 1.39 3.856 1.043 8.152 0 12.008-1.042 4.407-4.632 7.712-8.57 9.695-2.432 1.211-5.444 1.872-8.107.771-2.78-1.1-4.517-3.746-6.37-6.059-1.622 3.966-4.286 7.6-4.4 12.118a47.251 47.251 0 0 1-9.845 26c-9.844 12.89-24.9 20.6-40.188 25.119-8.8 2.423-17.952 4.627-27.217 4.186a28.549 28.549 0 0 1-14.362-3.856c-1.853-1.212-3.474-2.864-5.327-3.966-2.316-.441-4.749.22-7.181.22-5.791.221-11.581.441-17.256-.55 6.949-2.314 14.013-4.077 20.847-6.611a55.378 55.378 0 0 1 6.949-36.134 37.351 37.351 0 0 1 6.254-7.6c-1.969 3.966-4.4 7.822-5.444 12.229a51.829 51.829 0 0 0 .464 28.2c4.748-1.542 9.381-3.635 13.9-5.618 10.539-4.627 20.847-9.585 30.459-15.974 3.591-2.534 7.3-5.178 9.729-9.034 1.158-1.873.927-4.076.927-6.169-4.981.991-9.845 3.305-15.057 3.194-3.242-.22-6.717-.881-9.033-3.415s-2.432-6.169-1.969-9.364a18.062 18.062 0 0 1 5.906-11.127c3.822-3.195 9.5-3.085 13.9-1.1 4.286 1.984 6.718 6.17 8.8 10.136 6.485-2.424 10.886-8.152 17.256-10.907.232 3.305-.116 7.161 2.432 9.805 1.506 1.873 4.054 1.763 6.139 1.983-.348-5.288-1.158-10.466-1.853-15.644-2.2-17.3-4.4-34.7-6.254-52.109-.116-2.533-.464-5.177.81-7.271zm-39.609 75.245c-.579 1.211.811 1.983 1.737 2.423 3.475 1.212 7.3.441 10.887-.22-1.853-1.983-4.054-4.186-7.065-4.3-1.969-.11-4.748 0-5.559 2.094zm25.248 7.16c.232 3.526.347 7.051-.811 10.356-1.853 5.619-6.138 10.136-10.539 13.881-8.455 6.941-18.415 11.458-28.028 16.525a116.5 116.5 0 0 1-13.55 6.5c5.906.441 11.813-.551 17.6-.991 8.918-.551 17.488-3.085 25.711-6.39 8.571-3.525 16.909-8.042 23.627-14.652a31.11 31.11 0 0 0 8.686-14.542 41.364 41.364 0 0 0 .811-8.483c-3.475-.11-7.181 0-10.308-1.763-2.548-1.432-3.938-4.186-5.443-6.61a56.986 56.986 0 0 1-7.76 6.169zm94.159-82.735c1.274-1.432 3.59-1.212 5.1-.221a11.663 11.663 0 0 1 5.1 10.246 19.248 19.248 0 0 1-1.506 5.178c-.463-2.534-.347-5.178-1.5-7.491-1.737-2.424-4.98-2.865-7.065-4.848-.824-.661-1.171-2.203-.129-2.864z"
                        fill="#fff"
                      />
                      <path
                        d="M459.711 155.839c.232-1.543 1.737-3.195 3.359-2.314 4.517 2.2 7.759 6.39 9.844 10.907-1.621 0-3.243-.11-4.748-.22a63.172 63.172 0 0 0 2.895 9.033c1.737 4.187 4.17 8.153 4.864 12.78.811 5.067-.115 10.8-3.706 14.652-6.717 7.491-20.152 6.169-25.943-1.873-2.2-2.864-3.358-6.169-4.517-9.584-1.853 7.271-5.79 14.321-12.392 18.067a20.858 20.858 0 0 1-22-.771 14.1 14.1 0 0 1-6.6-11.568c-.463-6.83 2.2-13.22 3.127-19.83a14.493 14.493 0 0 0-3.822 5.839c-2.779 7.381-3.937 15.313-7.875 22.254-1.853 3.3-4.98 6.279-8.918 6.61-3.822.22-7.992-1.212-10.192-4.517-1.969-3.085-2.432-6.83-2.432-10.466a36.283 36.283 0 0 1 3.938-15.974c0 6.94-2.085 14.542 1.389 21.042 1.738 3.635 6.37 5.728 9.961 3.745 3.243-1.983 4.748-5.728 5.906-9.143 2.433-7.272 4.98-14.653 8.571-21.483 1.5-2.754 3.127-5.619 5.675-7.6 1.274-1.1 3.127-1.1 4.748-1.432.348 6.17-3.127 11.568-4.285 17.517-1.158 5.178-1.506 11.347 2.2 15.644 4.169 4.516 11.118 5.618 16.909 4.076 7.991-2.534 13.782-10.576 14.13-18.949.231-4.958.463-10.356-2.2-14.762-1.158-2.314-3.359-4.738-2.432-7.6a9.691 9.691 0 0 1 5.1-6.39 21.348 21.348 0 0 1 3.59 9.034c.927 7.6 1.042 15.423 3.706 22.694 1.622 4.187 4.054 8.483 8.339 10.466 3.938 1.873 8.8 2.314 12.624.331 2.432-1.212 3.011-4.186 2.432-6.61-1.621-7.932-7.064-14.212-9.612-21.813-1.39-3.856-2.433-7.822-1.622-11.788zm77.829-1.653c2.432-1.1 5.907-.22 7.065 2.314a2.616 2.616 0 0 1-1.274 2.974 22.19 22.19 0 0 1-4.749-1.872c-1.968 3.745-3.011 7.932-4.169 12.118a27.292 27.292 0 0 0-2.78-5.839c-1.621-2.2-4.169-3.305-6.022-5.288-.579-1.763 1.5-3.746 3.243-3.195 2.548.771 4.053 3.085 5.559 5.178.695-2.313 1.042-5.068 3.127-6.39zm-311.548 9.254c1.969-4.186 5.791-7.271 9.961-9.254-1.043 2.755-2.78 5.068-4.17 7.6-1.158 1.763-1.042 3.966-1.158 5.949a34.345 34.345 0 0 0 7.991-1.653c-.81 2.094-1.621 4.407-3.59 5.839-2.085 1.543-5.212 2.424-7.412.882-2.548-2.203-3.014-6.278-1.622-9.363z"
                        fill="#fff"
                      />
                      <path
                        d="M374.122 157.822c.695-3.636 6.139-3.526 7.876-.771 1.158 1.983 1.737 4.076 2.895 6.059 1.043 1.983 2.317 4.076 1.622 6.39-1.043 3.635-3.706 6.5-4.054 10.355 1.274-.551 2.548-1.212 3.822-1.873-.347 1.983 0 4.627-2.085 5.729-2.548 1.212-5.675-1.212-5.443-3.966.347-4.957 5.212-8.813 3.706-14.1-4.864 6.5-11.582 12.338-19.805 13.55 3.591-2.864 7.644-4.847 10.887-8.152a29.28 29.28 0 0 0 4.865-6.941c-1.738-1.762-4.749-3.415-4.286-6.279zm3.359-.331a9.135 9.135 0 0 0 3.011 2.865 4.5 4.5 0 0 0-3.011-2.865zm-29.07 2.644c1.737-.44 3.822-1.1 5.212.441a16.253 16.253 0 0 1 4.748 15.534c-.926-2.094-1.621-4.3-2.664-6.28-1.737-3.746-5.559-5.949-7.3-9.7zm-37.293 2.314a8.03 8.03 0 0 1 2.2 6.72c-2.779.441-5.559 1.212-8.338 1.873a2.889 2.889 0 0 1-3.475-2.2 17.682 17.682 0 0 1 .811-4.848c.579 1.653 1.158 3.3 1.737 5.068.695-.331 1.506-.551 2.2-.881v-4.517h1.621c.232 1.322.463 2.754.695 4.076.926-.11 1.737-.22 2.664-.331-.116-1.652-.116-3.305-.116-4.957zm235.224 3.635a24.528 24.528 0 0 1 6.949-.881 11.169 11.169 0 0 1-4.053 3.195c-7.992 3.195-16.215 5.508-23.859 9.584.695 1.433 1.39 2.755 2.085 4.187-1.853-.331-4.054-.331-5.443-1.873a3.089 3.089 0 0 1 .115-4.627 18.561 18.561 0 0 1 4.17-2.2c6.6-2.424 13.2-5.178 20.036-7.382zm-121.029 5.729a21.484 21.484 0 0 1 2.9-1.873c-.116 2.975.926 5.619 2.2 8.263s.232 6.389-2.895 7.05c-2.664.882-5.1-.771-6.949-2.313-1.506 1.322-3.127 3.085-5.328 2.754-4.053-.22-6.022-4.957-5.211-8.483.347-2.313 2.2-3.745 3.822-5.178-.232 2.424-1.159 4.848-.58 7.271.464 1.653 2.433 1.653 3.822 2.2.464-2.644.579-5.4 1.274-8.153.232-1.432 1.969-1.542 2.9-2.2.231 2.314.116 4.627.811 6.83.579 1.653 2.548 2.2 3.937 2.975.695-3.195-.695-6.169-.695-9.144zM305.1 176c1.738 0 3.822-.33 5.212 1.1a16.016 16.016 0 0 1 4.4 15.313c-1.39-3.635-2.548-7.6-5.559-10.355-1.853-1.652-3.71-3.525-4.053-6.058zm239.737 13.881a14.985 14.985 0 0 1 2.895-2.424 17.881 17.881 0 0 0 2.085 7.712c1.158 2.423 1.158 6.279-1.738 7.381-2.663 1.212-5.443-.221-7.644-1.763-1.737 1.322-3.821 2.975-6.254 2.314-3.59-.772-5.1-4.958-4.517-8.263.232-2.424 2.317-3.966 3.938-5.508-.347 2.534-1.39 5.067-.695 7.6.579 1.653 2.548 1.322 3.938 1.653.463-2.865.232-5.839 1.39-8.483a11.861 11.861 0 0 1 2.895-1.542c.116 2.533-.347 5.288.927 7.6.811 1.432 2.548 1.542 3.938 1.983-.116-2.754-.927-5.508-1.159-8.262zm96.243 3.966a2.617 2.617 0 0 1 2.548-3.526c2.9 0 5.1 2.424 6.486 4.737 2.432 4.738 3.59 10.466 1.622 15.534-1.043-4.517-.927-10.246-5.1-13.22-1.853-1.1-4.054-1.763-5.56-3.525zm-159.943 9.915a5.338 5.338 0 0 1 5.212-1.433c2.316 2.2 1.853 6.17 0 8.483-3.359 4.407-8.918 6.5-14.361 6.5 3.358-1.983 7.18-3.085 10.192-5.618 1.389-.992 1.158-2.865 1.389-4.407-1.621.22-3.243.551-4.748.771a22.265 22.265 0 0 1 2.316-4.3zm77.829 3.415c1.043-.221 2.432-.882 3.127.22-.81 2.644-3.59 3.746-5.791 4.847-8.338 3.856-16.446 8.153-24.669 12.339-.347-.771-.81-1.432-1.158-2.2 4.633-3.415 9.729-5.949 14.709-8.7 4.633-2.314 9.034-4.848 13.782-6.5zm-156.932 6.5c.232-1.653 1.39-2.865 2.317-4.077 6.369 6.941 11.813 14.983 14.708 24.127 2.78 8.7 3.475 17.847 5.212 26.771.927 4.517 1.737 9.254 4.749 12.889 1.968 2.534 4.864 4.076 7.644 5.619.579-2.865 1.274-5.949 3.937-7.6 8.107-5.4 17.489-8.7 26.754-11.677-7.528-.221-15.172 0-22.7-.111-6.138-.11-12.277.441-18.3-.661.231-2.313.231-4.737 1.621-6.83 2.78-4.627 7.644-7.271 12.277-9.695a113.428 113.428 0 0 1 21.889-8.923c-1.969-2.093-4.98-4.407-4.169-7.822 1.39-3.966 4.864-7.932 9.265-7.822 4.865 0 9.613 1.983 13.782 4.407 2.548 1.652 5.675 3.415 6.6 6.5a11.511 11.511 0 0 1-2.548 5.949c-2.664 3.305-7.412 3.084-11.35 3.525-.232 2.644-.579 5.839-3.243 7.051a138.064 138.064 0 0 0-2.548-6.17c-9.613 1.763-18.183 6.72-25.711 12.669 21.31.111 42.505-.44 63.815-.33 1.39.11 2.779.22 4.169.441-.926 5.618-6.6 8.372-11.581 9.584-10.076 2.2-20.153 4.187-30.229 6.17-7.528 1.542-15.172 3.305-22 6.72a17.906 17.906 0 0 0-6.37 5.618 92.647 92.647 0 0 0 15.4 3.966c1.737.441 4.169.771 4.633 2.865.694 2.754.81 6.61-1.854 8.372-3.358.772-6.833 0-10.191-.55-9.266-1.433-18.184-6.28-23.511-14.212-4.054-5.729-5.212-12.89-6.254-19.72a66.174 66.174 0 0 1-8.8 10.576c-9.034 8.483-19.921 14.652-31.271 19.61-3.938 1.763-8.918 2.313-12.856-.11-2.779-1.653-3.822-5.068-4.98-7.932-10.539 5.4-21.31 10.576-33.008 12.779a111.33 111.33 0 0 1-21.773 2.2c3.822-1.873 7.759-3.305 11.581-4.847 12.74-5.288 25.248-11.237 37.525-17.627 1.506-.881 3.359-1.652 4.4-3.084 1.158-3.195 1.274-6.61 3.011-9.695a22.076 22.076 0 0 1 8.223-8.814c-2.2 4.848-4.516 9.805-5.211 15.093a112.433 112.433 0 0 0 20.731-15.754c1.621-1.762 3.938-3.966 3.011-6.61-1.158-3.745-3.127-7.05-4.517-10.686-2.084-5.288-4.632-10.8-4.053-16.635.115-1.873 1.5-3.305 2.432-4.737 5.1 3.3 8.339 8.7 11.234 13.881-1.042.22-2.2.44-3.243.661 1.969 5.4 5.212 10.245 6.138 15.974a14.976 14.976 0 0 1-3.474 12.559c-7.876 8.483-17.6 14.873-26.754 21.813 1.622.882 3.359 2.093 5.328 1.983 3.822-.11 7.528-1.432 11-2.864 12.74-5.619 23.164-14.983 33.935-23.686-2.664-10.356-7.412-20.05-12.393-29.525a19.652 19.652 0 0 1-2.432-11.567zm61.5 13.66a11.091 11.091 0 0 0 5.1 3.966c3.127.661 6.138-1.211 8.455-3.194-2.9-2.2-6.255-3.966-9.961-3.966-1.737 0-3.59 1.432-3.59 3.194zm-220.864-13.66a2.727 2.727 0 0 1 2.664-2.644c3.938-.441 6.717 2.754 9.149 5.288 1.274 1.322 2.317 3.084 1.506 4.957-.463 2.093-2.9 1.983-4.517 2.424-3.474.22-4.864 3.966-7.528 5.618-5.328 3.636-11.929 6.5-18.531 5.619 1.622-2.314 4.286-3.305 6.834-4.076 4.748-1.543 8.57-4.958 12.045-8.483-.927-2.864-2.085-5.729-1.622-8.7zm2.2.44a13.389 13.389 0 0 0 1.737 5.509 81.937 81.937 0 0 0 1.969-4.738 36.657 36.657 0 0 0-3.705-.771zm5.211 7.161c1.274-.11 2.548-.33 3.822-.44a17.649 17.649 0 0 0-1.274-4.848c-1.621 1.212-1.853 3.415-2.548 5.288z"
                        fill="#fff"
                      />
                      <path
                        d="M439.327 214.558a9.711 9.711 0 0 1 2.78-1.983c0 3.856 2.779 6.83 3.127 10.576.347 3.856-4.633 5.839-7.644 4.076-.926-.661-1.737-1.432-2.548-2.093-1.621 1.432-3.359 3.525-5.791 2.975-3.706-.661-5.675-4.958-4.98-8.483.348-2.424 2.316-3.966 4.054-5.4-.348 2.314-1.158 4.517-.927 6.941.232 1.763 2.2 2.314 3.59 3.085a48.835 48.835 0 0 0 1.622-8.373c.232-1.542 2.085-1.763 3.127-2.534.116 2.314 0 4.737.695 6.941.695 1.542 2.316 2.313 3.59 3.195 1.39-2.975-.579-5.949-.695-8.924zm-184.844 14.101c1.969-.881 4.054-1.983 6.255-2.2 2.084-.11 3.127 3.085 1.5 4.407-2.432 1.873-5.327 2.644-8.107 3.856-6.486 3.084-12.972 6.059-19.341 9.474-1.39.881-3.128.661-4.633.441a10.975 10.975 0 0 1 4.053-4.738c6.723-4.079 13.44-7.824 20.273-11.24zm243.563 3.416c.464-2.2 3.012-2.534 4.865-3.195.463 3.195.811 6.5 2.779 9.144-1.042.11-2.084.22-3.011.33-1.39 2.093-2.895 4.407-5.212 5.508-3.822 1.873-8.107 1.873-12.276 1.653 4.4-1.212 9.844-1.542 12.74-5.508 1.969-2.314-.116-5.288.115-7.932zm87.442 3.194c.579-1.762 2.664-1.762 4.17-1.542 3.243.551 5.559 3.305 7.644 5.619a4.338 4.338 0 0 1 .579 5.728c-2.085 1.653-5.444.661-7.3 2.865-5.559 6.389-14.129 10.466-22.7 9.584 1.622-2.313 4.285-3.305 6.949-4.076 4.749-1.542 8.455-5.068 11.929-8.483-.926-3.085-2.316-6.39-1.274-9.695zm2.2 1.322a25.272 25.272 0 0 0 1.39 5.839 24.707 24.707 0 0 0 2.084-4.737c-1.157-.441-2.431-.771-3.473-1.102zm6.717 2.424a34.2 34.2 0 0 0-1.621 4.847c1.274-.11 2.548-.33 3.822-.44a20.233 20.233 0 0 0-1.274-4.517c-.232 0-.694.11-.926.11zm-228.042-.33c2.2-2.094 6.138-.992 7.528 1.542.695 1.432-.811 2.534-1.39 3.635a15.8 15.8 0 0 0-3.59-1.872c-1.159-.331-1.506.991-1.854 1.762-1.389 3.3-2.084 6.831-3.127 10.246a28.519 28.519 0 0 0-2.663-6.059c-1.39-1.983-3.707-3.085-5.444-4.738-1.621-1.322.116-4.186 1.969-3.855 2.664.33 4.285 2.754 6.138 4.516.695-1.762 1.159-3.745 2.433-5.177zm-139.212 19.169a21.931 21.931 0 0 1 9.612-8.483 44.366 44.366 0 0 1-4.169 7.6c-1.158 1.763-1.158 3.856-1.274 5.839a49.825 49.825 0 0 0 7.991-1.1c-.695 2.093-1.5 4.406-3.474 5.618-2.2 1.432-5.328 2.534-7.644.661a8.857 8.857 0 0 1-1.042-10.135zm184.843 16.965c1.969-.33 4.286-1.211 6.023.221a3.668 3.668 0 0 1 .695 5.508c-2.432 1.322-5.559.551-7.644 2.424.347 2.864 2.548 5.178 3.011 7.932.116 1.763-1.274 3.195-2.084 4.627-.58-2.754-1.043-5.729-2.548-8.152-.927-1.763-2.317-3.526-1.854-5.619.464-2.314 2.9-3.415 4.633-4.517a14.443 14.443 0 0 1-.232-2.424zm88.832 3.416c.927-2.314 3.127-3.416 5.1-4.627-.811 1.872-2.2 3.745-2.432 5.838.926 1.653 2.432 2.865 3.243 4.628.231 2.864-3.938 3.305-5.444 1.542-1.737-1.983-1.5-5.068-.463-7.381zM479.4 275.04c3.243-1.1 6.717 0 9.613 1.652-2.085 4.3-6.949 5.4-10.54 8.042a9.2 9.2 0 0 1 .232 1.763c2.548.11 5.212.441 7.412-.881a34.305 34.305 0 0 1 9.15-3.636c-2.548 2.2-4.98 4.407-7.528 6.5-3.012 2.424-7.181 2.424-10.771 1.432-1.737-.55-3.938-2.093-3.359-4.3.927-2.754 3.706-4.187 5.907-5.839-1.622-.11-3.127-.22-4.749-.331 1.274-1.758 2.433-3.962 4.633-4.402zm83.967 7.822a2.378 2.378 0 0 1 1.506-3.746c3.127-.551 5.791 1.983 7.181 4.517a12.389 12.389 0 0 1 .231 11.567c-.579-2.533-.579-5.177-1.853-7.491-1.737-2.313-4.98-2.754-7.065-4.847zm52.465 1.652c4.286-1.542 8.571-3.525 13.2-3.085a8.414 8.414 0 0 1-4.285 3.856c-7.18 2.644-14.477 5.068-21.542 8.043a36.523 36.523 0 0 1-6.949 2.313c-.231-.661-.579-1.322-.81-1.983 6.485-3.746 13.434-6.39 20.383-9.144zm-13.997 141.514h-35.672v-3.382c1.611-1.393 3.8-3.283 3.8-4.377 0-1.89-4.487-5.769-5.523-5.769-1.151 0-5.523 3.979-5.523 5.769 0 1.194 1.956 2.885 3.567 4.277l.115.1v3.481H281.25c10.011 9.947 24.855 9.848 54.658 10.146 26.581.4 26.466.4 226.573 0v3.581c-1.611 1.393-3.567 3.283-3.567 4.377 0 1.89 4.257 5.769 5.523 5.769.806 0 3.452-2.188 4.833-4.078h12.312c.806 1.392 2.647 2.188 4.833 2.188a5.645 5.645 0 0 0 4.833-2.188h14.729a7.361 7.361 0 0 0 5.869 2.188c5.638-.2 6.9-3.083 6.9-13.03-.115-8.356-5.753-9.251-16.915-9.052zm2.531 16.81h-13.118a6.247 6.247 0 0 0-9.665 0H569.5a14.382 14.382 0 0 0-3.222-3.083v-3.581h38.2a11.008 11.008 0 0 0-.112 6.664z"
                        fill="#fff"
                      />
                    </svg>
                  </button>
                </h3>
              </div>
              <div>
                <h3 className="font-semibold text-green-800  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm">
                  <Link to="artical">Articles</Link>
                </h3>
              </div>
            </div>
          )}
        </div>
        <div>
          <div className="bg-green-600  xl:w-28 lg:w-24 md:w-20 sm:w-16  max-sm:w-14 rounded-b-xl">
            <Link to="home">
              {" "}
              <img src={logo} alt="logo" />
            </Link>
          </div>
        </div>
      </div>
      {showHeader && (
        <div className=" py-2">
          <ul className="flex lg:space-x-4 mb-2  mt-4 flex-wrap max-lg:w-full   xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm font-semibold text-gray-600  rtl:space-x-reverse ">
            <ModalUl
              className="flex lg:flex-row flex-col lg:rtl:space-x-reverse "
              title="Scientists"
              subtitle={dataService}
            />
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>Education</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>Languages</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>Grants</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span>Tests</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>{" "}
            <li className="hover:text-green-600 group  cursor-pointer px-2 py-1 ">
              <span> University Specializations</span>
              <div className="h-0.5 mt-2 w-full group-hover:bg-green-600 "></div>
            </li>
            <ModalUl
              className=" flex lg:flex-row flex-col lg:rtl:space-x-reverse "
              title="Universities"
              subtitle={dataService}
            />
          </ul>
        </div>
      )}
    </div>
  );
};

export default Header;
