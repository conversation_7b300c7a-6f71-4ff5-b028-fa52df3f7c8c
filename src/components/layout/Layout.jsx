import React from "react";
import Header from "./Header";
import Footer from "./Footer";
import Home from "../../pages/Home";
import Artical from "../../pages/Artical";

import { Routes, Route } from "react-router-dom";

const Layout = () => {
  return (
    <div>
      <Header />
      <Routes>
        <Route index element={<Home />} />
        <Route path="home" element={<Home />} />
        <Route path="artical" element={<Artical />} />
      </Routes>
      <Footer />
    </div>
  );
};

export default Layout;
