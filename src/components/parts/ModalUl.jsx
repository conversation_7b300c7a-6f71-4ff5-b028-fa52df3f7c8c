import React, { useState } from "react";

function ModalUl({ className, subtitle, title }) {
  const [isHovered, setIsHovered] = useState(false);

  return (  
    <div className=" relative ">
      <p
        className="hover:text-green-600 transition-all duration-100 py-1  relative cursor-pointer  hover:border-b-2 hover:border-green-600"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {title}
      </p>
      {isHovered && (
        <ul
          className={`${className}  bg-white rounded-xl z-50  shadow-lg absolute py-5 px-4 font-semibold text-gray-600`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {subtitle.map((item) => (
            <li
              key={item.title}
              className={`hover:text-green-600   bg-white  xl:text-xl  lg:text-lg md:text-md sm:text-sm max-sm:text-sm transition-all duration-100 delay-100 text-lg cursor-pointer px-4 py-1 hover:border-s-4 hover:border-green-600`}
            >
              {item.title}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default ModalUl;
