import React, { useState } from "react";
import Universitesic from "../assets/images/main/Universitesic.png";
import home from "../assets/images/icons/home.svg";

import haert from "../assets/images/icons/haert.svg";
import message from "../assets/images/icons/message.svg";
import eay from "../assets/images/icons/eay.svg";
import share from "../assets/images/icons/share.svg";

import { useTranslation } from "react-i18next";

import {
  WhatsappShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterShareButton,
} from "react-share";
import {
  WhatsappIcon,
  FacebookIcon,
  LinkedinIcon,
  TwitterIcon,
} from "react-share";

function ArticalAr() {
  const [showArticle, setShowArticle] = useState(false);
  const { t, i18n } = useTranslation();
  const data = [
    {
      id: 1,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 2,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 3,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 4,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 5,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 6,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 7,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
    {
      id: 8,
      title: "جامعة ماردين",
      subtitle: "جامعة تركية",
    },
  ];
  return (
    <>
      <div className="lg:px-12 px-8  lg:mt-14 mt-2 max-w-screen-xl lg:ms-12 ">
        <div className="fixed flex flex-col w-16 space-y-3  xl:left-20 lg:left-16 md:left-14 sm:left-8 max-sm:left-4 top-72">
          <FacebookShareButton
            className="  rounded-full"
            url={window.location.href}
          >
            <FacebookIcon className="  xl:w-16  xl:h-16  lg:w-14  lg:h-14 md:w-12  md:h-12 sm:w-11  sm:h-11  max-sm:w-9  max-sm:h-9  rounded-full" />
          </FacebookShareButton>
          <WhatsappShareButton
            className=" rounded-full"
            url={window.location.href}
          >
            <WhatsappIcon className="  xl:w-16  xl:h-16  lg:w-14  lg:h-14 md:w-12  md:h-12 sm:w-11  sm:h-11  max-sm:w-9  max-sm:h-9 rounded-full" />
          </WhatsappShareButton>
          <LinkedinShareButton
            className=" rounded-full"
            url={window.location.href}
          >
            <LinkedinIcon className="  xl:w-16  xl:h-16  lg:w-14  lg:h-14 md:w-12  md:h-12 sm:w-11  sm:h-11  max-sm:w-9  max-sm:h-9 rounded-full" />
          </LinkedinShareButton>
          <TwitterShareButton
            className=" rounded-full"
            url={window.location.href}
          >
            <TwitterIcon className=" xl:w-16  xl:h-16  lg:w-14  lg:h-14 md:w-12  md:h-12 sm:w-11  sm:h-11  max-sm:w-9  max-sm:h-9 rounded-full" />
          </TwitterShareButton>
        </div>
        <div>
          <div className="flex space-x-4 xl:text-2xl  lg:text-xl md:text-lg sm:text-sm max-sm:text-sm text-green-600 font-semibold items-center rtl:space-x-reverse px-4 py-2 mb-3 bg-gray-200 rounded-xl ">
            <span>
              <img src={home} alt="home" />
            </span>
            <span>/ </span>
            <h1>مقالات</h1>

            <span>/ </span>
            <h1>الجامعة</h1>
            <span>/ </span>
            <h1>ماردين</h1>
          </div>
          <img className="rounded-xl" src={Universitesic} alt="" />
          <article>
            <div className="p-8  max-w-screen-lg lg:ms-8 bg-white shadow-xl lg:-translate-y-44  max-lg:mt-4   rounded-xl ">
              <div className="flex max-lg:flex-col lg:items-center  max-lg:space-y-5  justify-between">
                <ul className="flex  space-x-4 text-green-900 font-medium rtl:space-x-reverse">
                  <li> نوع الصنف </li>
                  <li>التقييم</li>
                  <li>خاص</li>
                </ul>
                <div className="flex space-x-4 items-center rtl:space-x-reverse">
                  <div className="flex max-lg:flex-col max-lg:justify-center   px-4 py-2  bg-gray-200 rounded-xl items-center  rtl:space-x-reverse lg:space-x-2 ">
                    <span>
                      {" "}
                      <img src={haert} alt="haert" />
                    </span>
                    <span className=" lg:text-lg md:text-md sm:text-sm max-sm:text-xs">
                      195
                    </span>
                  </div>
                  <div className="flex max-lg:flex-col max-lg:justify-center   px-4 py-2  bg-gray-200 rounded-xl items-center  rtl:space-x-reverse lg:space-x-2 ">
                    <span>
                      {" "}
                      <img src={eay} alt="eay" />
                    </span>
                    <span className=" lg:text-lg md:text-md sm:text-sm max-sm:text-xs">
                      195
                    </span>
                  </div>
                  <div className="flex max-lg:flex-col max-lg:justify-center   px-4 py-2  bg-gray-200 rounded-xl items-center  rtl:space-x-reverse lg:space-x-2 ">
                    <span>
                      {" "}
                      <WhatsappShareButton
                        className=""
                        url={window.location.href}
                      >
                        <img src={share} alt="share" />
                      </WhatsappShareButton>
                    </span>
                    <span className=" lg:text-lg md:text-md sm:text-sm max-sm:text-xs">
                      195
                    </span>
                  </div>
                </div>
              </div>

              <div className="text-green-900">
                <h3 className="font-bold xl:text-4xl  lg:text-3xl md:text-2xl sm:text-xl max-sm:text-xl    mb-8">
                  ماردين
                </h3>
                <p className="font-semibold xl:text-2xl  lg:text-xl md:text-lg sm:text-sm max-sm:text-sm">
                  جامعة ماردين آرتوقلو هي جامعة عامة تُعتبر واحدة من الجامعات
                  الجديدة التي تأسست في تركيا عام 2007. تتميز الجامعة بتكاليفها
                  المنخفضة للتخصصات المتاحة. أعلنت الجامعة عن افتتاح كلية تمريض
                  باللغة العربية، مما يجعلها الجامعة الوحيدة التي تدرس هذا
                  التخصص باللغة العربية. تجعل هذه الفرادة منها وجهة متميزة
                  للطلاب الدوليين عمومًا والطلاب العرب بشكل خاص. بالإضافة إلى
                  تفوقها وتقدمها، تقبل الجامعة عددًا كبيرًا من الطلاب الدوليين
                  كل عام في كلياتها المتميزة.
                </p>
                <p
                  className={` ${
                    showArticle ? "visible" : "hidden"
                  } font-semibold xl:text-2xl  lg:text-xl md:text-lg sm:text-sm max-sm:text-sm`}
                >
                  جامعة ماردين آرتوقلو هي جامعة عامة تُعتبر واحدة من الجامعات
                  الجديدة التي تأسست في تركيا عام 2007. تتميز الجامعة بتكاليفها
                  المنخفضة للتخصصات المتاحة. أعلنت الجامعة عن افتتاح كلية تمريض
                  باللغة العربية، مما يجعلها الجامعة الوحيدة التي تدرس هذا
                  التخصص باللغة العربية. تجعل هذه الفرادة منها وجهة متميزة
                  للطلاب الدوليين عمومًا والطلاب العرب بشكل خاص. بالإضافة إلى
                  تفوقها وتقدمها، تقبل الجامعة عددًا كبيرًا من الطلاب الدوليين
                  كل عام في كلياتها المتميزة. جامعة ماردين آرتوقلو هي جامعة عامة
                  تُعتبر واحدة من الجامعات الجديدة التي تأسست في تركيا عام 2007.
                  تتميز الجامعة بتكاليفها المنخفضة للتخصصات المتاحة. أعلنت
                  الجامعة عن افتتاح كلية تمريض باللغة العربية، مما يجعلها
                  الجامعة الوحيدة التي تدرس هذا التخصص باللغة العربية. تجعل هذه
                  الفرادة منها وجهة متميزة للطلاب الدوليين عمومًا والطلاب العرب
                  بشكل خاص. بالإضافة إلى تفوقها وتقدمها، تقبل الجامعة عددًا
                  كبيرًا من الطلاب الدوليين كل عام في كلياتها المتميزة.
                </p>
              </div>
              <div className="flex justify-end ">
                <button
                  onClick={() => {
                    setShowArticle(!showArticle);
                  }}
                  className="font-bold  mt-4 text-green-900 text-2xl "
                  href="#"
                >
                  {` ${showArticle ? "إخفاء" : "عرض المزيد"} `}
                </button>
              </div>
            </div>
          </article>

          {/* <ul className="flex space-x-4 mb-2   text-xl font-semibold text-gray-600  rtl:space-x-reverse ">
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            منح تركية
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            منوع
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            تخصصات جامعية
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            علماء
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            أدبي
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            علمي
          </li>
          <li className="hover:text-green-600 cursor-pointer px-2 py-1 hover:border-b-2 hover:border-green-600">
            الجامعات
          </li>
        </ul> */}
        </div>
      </div>
      <div className="lg:px-12 px-8  lg:mt-14 mt-8 max-w-screen-xl lg:ms-12 text-green-900">
        <div className="mb-8">
          <h5 className="font-bold  xl:text-4xl  lg:text-3xl md:text-2xl sm:text-xl max-sm:text-xl">
            المقالات المتعلقة
          </h5>
        </div>
        <div className="grid  xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2  sm:grid-cols-1 max-sm:grid-cols-1  gap-16 ">
          {data.map((itme) => (
            <div key={itme.id} className="rounded-xl">
              <img
                className="rounded-xl"
                src={Universitesic}
                alt="Universitesic"
              />
              <h3 className="font-bold p-4   xl:text-3xl  lg:text-2xl md:text-xl sm:text-lg max-sm:text-md">
                {t(itme.title)}
              </h3>
              <h4 className="font-semibold p-4 text-xl"> {itme.subtitle}</h4>
            </div>
          ))}
        </div>
      </div>
      {/* <div className="   ltr:bottom-12 ltr:right-10 bottom-12  left-10 absolute">
        <div className="py-6 px-6   bg-green-600 items-center rounded-full ">
          <img className="m-auto" src={message} alt="" />
        </div>
      </div> */}
    </>
  );
}
export default ArticalAr;
