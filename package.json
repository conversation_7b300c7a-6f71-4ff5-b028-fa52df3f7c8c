{"name": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"i18next": "^23.7.19", "i18next-browser-languagedetector": "^7.2.0", "localforage": "^1.10.0", "match-sorter": "^6.3.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.1", "react-router-dom": "^6.21.3", "react-share": "^5.0.3", "sharethis-reactjs": "^1.6.0", "sort-by": "^1.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^5.0.8"}}